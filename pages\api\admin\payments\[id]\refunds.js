import { authenticateAdminRequest } from '@/lib/admin-auth'
import { getAdminClient } from '@/lib/supabase'

/**
 * API endpoint for retrieving payment refund history
 * GET /api/admin/payments/[id]/refunds - Get all refunds for a payment
 * 
 * Security: Only Admin and DEV roles can view refunds
 */
export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7)
  const { id: paymentId } = req.query

  console.log(`[${requestId}] Refund history API called for payment: ${paymentId}`)

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate and authorize request
    const { user, role, authorized, error: authError } = await authenticateAdminRequest(req)

    if (authError || !authorized) {
      console.log(`[${requestId}] Authentication failed:`, authError?.message)
      return res.status(401).json({ 
        error: 'Unauthorized',
        message: authError?.message || 'Authentication required'
      })
    }

    // Check role permissions - only Admin and DEV can view refunds
    if (!['admin', 'dev'].includes(role)) {
      console.log(`[${requestId}] Insufficient permissions. Role: ${role}`)
      return res.status(403).json({ 
        error: 'Forbidden',
        message: 'Only Admin and DEV users can view refunds'
      })
    }

    const supabaseAdmin = getAdminClient()

    // Verify payment exists
    const { data: payment, error: paymentError } = await supabaseAdmin
      .from('payments')
      .select('id, amount, currency, payment_status')
      .eq('id', paymentId)
      .single()

    if (paymentError || !payment) {
      console.error(`[${requestId}] Payment not found:`, paymentError)
      return res.status(404).json({
        error: 'Payment not found',
        message: 'The specified payment does not exist'
      })
    }

    // Get refunds for this payment
    const { data: refunds, error: refundsError } = await supabaseAdmin
      .from('refunds')
      .select(`
        id,
        refund_amount,
        currency,
        refund_reason,
        refund_notes,
        refund_method,
        refund_status,
        square_refund_id,
        processed_by,
        processed_at,
        completed_at,
        created_at,
        updated_at,
        processed_by_user:processed_by (
          id,
          email
        )
      `)
      .eq('payment_id', paymentId)
      .order('created_at', { ascending: false })

    if (refundsError) {
      console.error(`[${requestId}] Error fetching refunds:`, refundsError)
      return res.status(500).json({
        error: 'Failed to fetch refunds',
        message: refundsError.message
      })
    }

    // Calculate refund summary
    const totalRefunded = refunds
      .filter(refund => refund.refund_status === 'completed')
      .reduce((sum, refund) => sum + parseFloat(refund.refund_amount), 0)

    const pendingRefunds = refunds
      .filter(refund => refund.refund_status === 'pending')
      .reduce((sum, refund) => sum + parseFloat(refund.refund_amount), 0)

    const remainingAmount = parseFloat(payment.amount) - totalRefunded

    // Format refunds for response
    const formattedRefunds = refunds.map(refund => ({
      id: refund.id,
      refund_amount: parseFloat(refund.refund_amount),
      currency: refund.currency,
      refund_reason: refund.refund_reason,
      refund_notes: refund.refund_notes,
      refund_method: refund.refund_method,
      refund_status: refund.refund_status,
      square_refund_id: refund.square_refund_id,
      processed_by: {
        id: refund.processed_by,
        email: refund.processed_by_user?.email || 'Unknown'
      },
      processed_at: refund.processed_at,
      completed_at: refund.completed_at,
      created_at: refund.created_at,
      updated_at: refund.updated_at
    }))

    console.log(`[${requestId}] Retrieved ${refunds.length} refunds for payment ${paymentId}`)

    return res.status(200).json({
      success: true,
      payment: {
        id: payment.id,
        amount: parseFloat(payment.amount),
        currency: payment.currency,
        payment_status: payment.payment_status
      },
      refund_summary: {
        total_refunded: totalRefunded,
        pending_refunds: pendingRefunds,
        remaining_amount: remainingAmount,
        can_refund: remainingAmount > 0 && payment.payment_status === 'completed'
      },
      refunds: formattedRefunds,
      total_refunds: refunds.length
    })

  } catch (error) {
    console.error(`[${requestId}] Refund history API Error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to fetch refund history'
    })
  }
}
