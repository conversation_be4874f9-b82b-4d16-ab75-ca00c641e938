-- =============================================
-- OCEAN SOUL SPARKLES REFUND SYSTEM
-- =============================================
-- Migration: Create refunds table and related functionality
-- Created: 2025-01-08
-- Purpose: Implement secure refund processing system with role-based access control

-- =============================================
-- CREATE REFUNDS TABLE
-- =============================================

CREATE TABLE IF NOT EXISTS public.refunds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  payment_id UUID REFERENCES public.payments(id) NOT NULL,
  refund_amount DECIMAL(10, 2) NOT NULL,
  currency TEXT NOT NULL DEFAULT 'AUD',
  refund_reason TEXT NOT NULL,
  refund_notes TEXT,
  refund_method TEXT NOT NULL, -- 'cash', 'square_api', 'manual'
  refund_status TEXT NOT NULL DEFAULT 'pending', -- 'pending', 'completed', 'failed', 'cancelled'
  square_refund_id TEXT, -- For Square API refunds
  processed_by UUID REFERENCES auth.users(id) NOT NULL,
  processed_at TIMESTAMPTZ DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Constraints
  CHECK (refund_amount > 0),
  CHECK (refund_reason IN ('customer_request', 'service_issue', 'billing_error', 'other')),
  CHECK (refund_method IN ('cash', 'square_api', 'manual')),
  CHECK (refund_status IN ('pending', 'completed', 'failed', 'cancelled'))
);

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

CREATE INDEX IF NOT EXISTS idx_refunds_payment_id ON public.refunds(payment_id);
CREATE INDEX IF NOT EXISTS idx_refunds_processed_by ON public.refunds(processed_by);
CREATE INDEX IF NOT EXISTS idx_refunds_status ON public.refunds(refund_status);
CREATE INDEX IF NOT EXISTS idx_refunds_created_at ON public.refunds(created_at);
CREATE INDEX IF NOT EXISTS idx_refunds_square_refund_id ON public.refunds(square_refund_id) WHERE square_refund_id IS NOT NULL;

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

ALTER TABLE public.refunds ENABLE ROW LEVEL SECURITY;

-- =============================================
-- CREATE RLS POLICIES
-- =============================================

-- Admin and DEV users can view all refunds
CREATE POLICY "Admin and DEV can view all refunds" ON public.refunds
  FOR SELECT USING (
    get_user_role(auth.uid()) IN ('admin', 'dev')
  );

-- Admin and DEV users can insert refunds
CREATE POLICY "Admin and DEV can insert refunds" ON public.refunds
  FOR INSERT WITH CHECK (
    get_user_role(auth.uid()) IN ('admin', 'dev')
  );

-- Admin and DEV users can update refunds
CREATE POLICY "Admin and DEV can update refunds" ON public.refunds
  FOR UPDATE USING (
    get_user_role(auth.uid()) IN ('admin', 'dev')
  );

-- No delete policy - refunds should not be deleted for audit purposes

-- =============================================
-- CREATE HELPER FUNCTIONS
-- =============================================

-- Function to get total refunded amount for a payment
CREATE OR REPLACE FUNCTION public.get_payment_refunded_amount(payment_uuid UUID)
RETURNS DECIMAL(10, 2) AS $$
DECLARE
  total_refunded DECIMAL(10, 2);
BEGIN
  SELECT COALESCE(SUM(refund_amount), 0)
  INTO total_refunded
  FROM public.refunds
  WHERE payment_id = payment_uuid
    AND refund_status = 'completed';
  
  RETURN total_refunded;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if payment can be refunded
CREATE OR REPLACE FUNCTION public.can_refund_payment(payment_uuid UUID, refund_amount_param DECIMAL(10, 2))
RETURNS BOOLEAN AS $$
DECLARE
  payment_amount DECIMAL(10, 2);
  total_refunded DECIMAL(10, 2);
  payment_status TEXT;
BEGIN
  -- Get payment details
  SELECT amount, payment_status
  INTO payment_amount, payment_status
  FROM public.payments
  WHERE id = payment_uuid;
  
  -- Check if payment exists and is completed
  IF payment_amount IS NULL OR payment_status != 'completed' THEN
    RETURN FALSE;
  END IF;
  
  -- Get total already refunded
  total_refunded := get_payment_refunded_amount(payment_uuid);
  
  -- Check if refund amount would exceed payment amount
  IF (total_refunded + refund_amount_param) > payment_amount THEN
    RETURN FALSE;
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update payment status based on refunds
CREATE OR REPLACE FUNCTION public.update_payment_refund_status()
RETURNS TRIGGER AS $$
DECLARE
  payment_amount DECIMAL(10, 2);
  total_refunded DECIMAL(10, 2);
  new_status TEXT;
BEGIN
  -- Only process for completed refunds
  IF NEW.refund_status != 'completed' THEN
    RETURN NEW;
  END IF;
  
  -- Get payment amount
  SELECT amount INTO payment_amount
  FROM public.payments
  WHERE id = NEW.payment_id;
  
  -- Get total refunded amount
  total_refunded := get_payment_refunded_amount(NEW.payment_id);
  
  -- Determine new payment status
  IF total_refunded >= payment_amount THEN
    new_status := 'refunded';
  ELSIF total_refunded > 0 THEN
    new_status := 'partially_refunded';
  ELSE
    new_status := 'completed';
  END IF;
  
  -- Update payment status
  UPDATE public.payments
  SET 
    payment_status = new_status,
    updated_at = NOW()
  WHERE id = NEW.payment_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- CREATE TRIGGERS
-- =============================================

-- Trigger to update payment status when refund is completed
CREATE TRIGGER trigger_update_payment_refund_status
  AFTER INSERT OR UPDATE ON public.refunds
  FOR EACH ROW
  EXECUTE FUNCTION update_payment_refund_status();

-- Trigger to update refunds updated_at timestamp
CREATE OR REPLACE FUNCTION public.update_refunds_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_refunds_updated_at
  BEFORE UPDATE ON public.refunds
  FOR EACH ROW
  EXECUTE FUNCTION update_refunds_updated_at();

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant necessary permissions to authenticated users
GRANT SELECT ON public.refunds TO authenticated;
GRANT INSERT ON public.refunds TO authenticated;
GRANT UPDATE ON public.refunds TO authenticated;

-- Grant permissions on helper functions
GRANT EXECUTE ON FUNCTION public.get_payment_refunded_amount(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.can_refund_payment(UUID, DECIMAL) TO authenticated;

-- =============================================
-- MIGRATION COMPLETE
-- =============================================

-- Add comment to track migration
COMMENT ON TABLE public.refunds IS 'Refund processing system for Ocean Soul Sparkles - Created 2025-01-08';
